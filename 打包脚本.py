#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask游戏资源管理系统 - PyInstaller打包脚本
功能：将整个项目打包成单个可执行文件(.exe)
作者：AI Assistant
版本：1.0
"""

import os
import sys
import shutil
import subprocess
import tempfile
from pathlib import Path

class GameResourceManagerBuilder:
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        self.spec_file = self.project_root / "SteamVault Pro - Steam 宝库.spec"
        
        # 项目信息
        self.app_name = "SteamVault Pro - Steam 宝库"
        self.app_version = "1.0.0"
        self.app_description = "SteamVault Pro - Steam 宝库"
        self.app_author = "SteamVault Pro"
        
        # 文件路径
        self.main_script = self.project_root / "main.py"
        self.icon_file = self.project_root / "static" / "icons8-游戏文件夹-50_64x64.ico"
        
    def check_dependencies(self):
        """检查必要的依赖是否已安装"""
        print("🔍 检查依赖...")

        required_packages = [
            ('PyInstaller', 'pyinstaller'),
            ('flask', 'flask'),
            ('requests', 'requests'),
            ('patoolib', 'patoolib'),
            ('Crypto', 'pycryptodome'),
            ('customtkinter', 'customtkinter'),
            ('psutil', 'psutil')  # 新增：用于进程管理
        ]

        missing_packages = []
        for import_name, package_name in required_packages:
            try:
                __import__(import_name)
                print(f"  ✅ {package_name}")
            except ImportError:
                missing_packages.append(package_name)
                print(f"  ❌ {package_name} (未安装)")

        if missing_packages:
            print(f"\n❌ 缺少依赖包: {', '.join(missing_packages)}")
            print("请运行以下命令安装:")
            print(f"pip install {' '.join(missing_packages)}")
            return False

        print("✅ 所有依赖检查完成")
        return True

    def check_project_structure(self):
        """检查项目结构完整性"""
        print("🔍 检查项目结构...")

        # 必需的文件和目录
        required_items = [
            ('main.py', '主程序文件'),
            ('登录窗口.py', '登录窗口文件'),
            ('模块', '模块目录'),
            ('templates', '模板目录'),
            ('static', '静态文件目录'),
            ('安装包文件', '安装包目录'),
            ('配置文件.zip', '配置文件'),
        ]

        missing_items = []
        for item_path, description in required_items:
            full_path = self.project_root / item_path
            if full_path.exists():
                print(f"  ✅ {description}: {item_path}")
            else:
                missing_items.append((item_path, description))
                print(f"  ⚠️ {description}: {item_path} (缺失)")

        # 检查模块文件
        module_files = [
            '配置.py', '日志.py', '网页显示.py', '网络请求.py',
            '数据处理.py', 'VDF处理.py', '配置检测.py', 'Steam功能.py',
            '路径工具.py', '网络验证.py'
        ]

        module_dir = self.project_root / "模块"
        if module_dir.exists():
            for module_file in module_files:
                module_path = module_dir / module_file
                if module_path.exists():
                    print(f"  ✅ 模块文件: {module_file}")
                else:
                    print(f"  ⚠️ 模块文件: {module_file} (缺失)")

        if missing_items:
            print(f"\n⚠️ 发现 {len(missing_items)} 个缺失项目，但将继续打包")
            for item_path, description in missing_items:
                print(f"  - {description}: {item_path}")

        print("✅ 项目结构检查完成")
        return True

    def clean_build_dirs(self):
        """清理之前的构建目录"""
        print("🧹 清理构建目录...")
        
        dirs_to_clean = [self.build_dir, self.dist_dir]
        for dir_path in dirs_to_clean:
            if dir_path.exists():
                shutil.rmtree(dir_path)
                print(f"  🗑️ 删除 {dir_path}")
        
        if self.spec_file.exists():
            os.remove(self.spec_file)
            print(f"  🗑️ 删除 {self.spec_file}")
    
    def create_spec_file(self):
        """创建PyInstaller规格文件"""
        print("📝 创建PyInstaller规格文件...")
        
        # 收集所有需要包含的数据文件
        datas = [
            ('templates', 'templates'),
            ('static', 'static'),
            ('安装包文件', '安装包文件'),
        ]

        # 检查配置文件是否存在
        config_zip = self.project_root / "配置文件.zip"
        if config_zip.exists():
            datas.append(('配置文件.zip', '.'))

        # 检查登录配置文件是否存在
        login_config = self.project_root / "登录配置.json"
        if login_config.exists():
            datas.append(('登录配置.json', '.'))



        # 检查登录窗口.py文件是否存在
        login_window = self.project_root / "登录窗口.py"
        if login_window.exists():
            datas.append(('登录窗口.py', '.'))

        # 检查图标文件
        icon_path = None
        if self.icon_file.exists():
            icon_path = self.icon_file.as_posix()

        # 隐藏导入的模块
        hiddenimports = [
            'flask',
            'requests',
            'patoolib',
            'winreg',
            'subprocess',
            'threading',
            'tempfile',
            'shutil',
            'urllib.request',
            'urllib.parse',
            'json',
            'logging',
            'time',
            'os',
            'sys',
            'io',
            'pathlib',
            'tkinter',
            'tkinter.messagebox',
            'customtkinter',
            'Crypto.Cipher.DES',
            'Crypto.Util.Padding',
            'platform',
            'uuid',
            'hashlib',
            'base64',
            'typing',
            'webbrowser',
            'zipfile',  # 新增：用于配置文件处理
            'psutil',   # 新增：用于进程管理
            're',       # 新增：用于正则表达式处理
            'atexit',   # 新增：用于程序退出处理
            '模块.配置',
            '模块.日志',
            '模块.网页显示',
            '模块.网络请求',
            '模块.数据处理',
            '模块.VDF处理',
            '模块.配置检测',
            '模块.Steam功能',
            '模块.路径工具',
            '模块.网络验证',
        ]
        
        # 排除不需要的模块
        excludes = [
            'matplotlib',
            'numpy',
            'pandas',
            'scipy',
            'PIL',
            'cv2',
            'tensorflow',
            'torch',
            'jupyter',
            'IPython',
            'pytest',      # 新增：测试框架
            'unittest',    # 新增：单元测试
            'doctest',     # 新增：文档测试
        ]
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['{self.main_script.as_posix()}'],
    pathex=['{self.project_root.as_posix()}'],
    binaries=[],
    datas={datas},
    hiddenimports={hiddenimports},
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes={excludes},
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{self.app_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='{icon_path}',
    version_file=None,
)
'''
        
        with open(self.spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        print(f"  ✅ 规格文件已创建: {self.spec_file}")
    
    def build_executable(self):
        """使用PyInstaller构建可执行文件"""
        print("🔨 开始构建可执行文件...")
        
        # PyInstaller命令
        cmd = [
            'pyinstaller',
            '--clean',
            '--noconfirm',
            str(self.spec_file)
        ]
        
        print(f"  📋 执行命令: {' '.join(cmd)}")
        
        try:
            # 执行构建
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                encoding='gbk',
                errors='ignore'
            )
            
            if result.returncode == 0:
                print("  ✅ 构建成功!")
                return True
            else:
                print("  ❌ 构建失败!")
                print("错误输出:")
                print(result.stderr)
                return False
                
        except Exception as e:
            print(f"  ❌ 构建过程中发生异常: {e}")
            return False
    
    def post_build_tasks(self):
        """构建后的任务"""
        print("🔧 执行构建后任务...")
        
        exe_file = self.dist_dir / f"{self.app_name}.exe"
        
        if exe_file.exists():
            file_size = exe_file.stat().st_size / (1024 * 1024)  # MB
            print(f"  ✅ 可执行文件已生成: {exe_file}")
            print(f"  📊 文件大小: {file_size:.2f} MB")
            

            
            return True
        else:
            print("  ❌ 未找到生成的可执行文件")
            return False

    
    def build(self):
        """执行完整的构建流程"""
        print(f"🚀 开始构建 {self.app_name} v{self.app_version}")
        print("=" * 50)

        # 检查依赖
        if not self.check_dependencies():
            return False

        # 检查项目结构
        if not self.check_project_structure():
            return False

        # 清理构建目录
        self.clean_build_dirs()

        # 创建规格文件
        self.create_spec_file()

        # 构建可执行文件
        if not self.build_executable():
            return False

        # 构建后任务
        if not self.post_build_tasks():
            return False

        print("=" * 50)
        print("🎉 构建完成!")
        print(f"📁 输出目录: {self.dist_dir}")
        print(f"🎯 可执行文件: {self.dist_dir / f'{self.app_name}.exe'}")
        print("💡 双击exe文件启动程序，会自动打开浏览器")
        print("💡 首次运行会显示登录窗口，请输入激活码")
        print("=" * 50)

        return True

def main():
    """主函数"""
    builder = GameResourceManagerBuilder()
    
    try:
        success = builder.build()
        if success:
            print("\n✅ 打包成功! 可以在dist目录中找到生成的可执行文件。")
            sys.exit(0)
        else:
            print("\n❌ 打包失败! 请检查错误信息。")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断了构建过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 构建过程中发生未预期的错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
